:root {
  --bg: #0f172a;        /* slate-900 */
  --card: #111827;      /* gray-900 */
  --muted: #94a3b8;     /* slate-400 */
  --text: #e5e7eb;      /* gray-200 */
  --primary: #22d3ee;   /* cyan-400 */
  --primary-700: #0e7490;/* cyan-700 */
  --ring: rgba(34, 211, 238, 0.45);
  --error: #ef4444;     /* red-500 */
  --success: #10b981;   /* emerald-500 */
  --warning: #f59e0b;   /* amber-500 */
  --border: rgba(148, 163, 184, 0.2);
}

* { box-sizing: border-box; }
html, body { height: 100%; }

/* Navigation */
.nav-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.15);
  z-index: 1000;
  padding: 0.75rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  color: var(--text);
  text-decoration: none;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary), #3b82f6);
  border-radius: 8px;
  display: grid;
  place-items: center;
  font-size: 1rem;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.nav-link {
  color: var(--text);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: var(--primary);
  background: rgba(34, 211, 238, 0.1);
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  background: radial-gradient(1200px 800px at 80% -10%, rgba(34,211,238,.12), transparent 60%),
              radial-gradient(900px 700px at -10% 110%, rgba(59,130,246,.10), transparent 60%),
              var(--bg);
  color: var(--text);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 1rem;
  padding-top: 5rem; /* Account for fixed navigation */
  min-height: 100vh;
}

.card {
  width: 100%;
  max-width: 800px;
  background: linear-gradient(180deg, rgba(255,255,255,0.02), transparent 40%), var(--card);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 1.25rem;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35), inset 0 1px 0 rgba(255,255,255,0.02);
  padding: 2rem;
  margin: 1rem 0;
}

.logo {
  width: 48px; height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary), #3b82f6);
  display: grid; place-items: center;
  color: #0b1220;
  font-weight: 800;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

h1 { 
  font-size: 1.75rem; 
  margin: 0 0 .5rem; 
  font-weight: 700;
}

p { 
  margin: 0 0 2rem; 
  color: var(--muted); 
  font-size: 1.1rem;
}

.registration-form { 
  display: flex;
  flex-direction: column;
  gap: 2rem; 
}

/* Form Sections */
.form-section {
  background: rgba(2,6,23,.3);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 1.5rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

/* Labels and Inputs */
label { 
  font-size: .9rem; 
  color: #cbd5e1; 
  display: block; 
  margin-bottom: .5rem;
  font-weight: 500;
}

.input {
  width: 100%;
  border: 1px solid var(--border);
  background: rgba(2,6,23,.6);
  color: var(--text);
  padding: .85rem 1rem;
  border-radius: .75rem;
  outline: none;
  transition: border-color .2s, box-shadow .2s, background .2s;
  font-size: 1rem;
  min-height: 44px; /* Minimum touch target size */
}

.input::placeholder { 
  color: var(--muted); 
}

.input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--ring);
  background: rgba(2,6,23,.9);
}

.input:invalid:not(:placeholder-shown) {
  border-color: var(--error);
}

.input:valid:not(:placeholder-shown) {
  border-color: var(--success);
}

/* Select Styling */
select.input {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Textarea Styling */
textarea.input {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

/* Input Hints */
.input-hint {
  font-size: 0.8rem;
  color: var(--muted);
  margin-top: 0.25rem;
  font-style: italic;
}

/* Checkbox Styling */
.checkbox-group {
  margin-bottom: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.5;
}

.checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--border);
  border-radius: 4px;
  background: rgba(2,6,23,.6);
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #0b1220;
  font-weight: bold;
  font-size: 12px;
}

.checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--ring);
}

/* Links */
.link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

/* Error Messages */
.error-messages {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.error-messages ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--error);
}

.error-messages li {
  margin-bottom: 0.5rem;
}

.error-messages li:last-child {
  margin-bottom: 0;
}

/* Button Styling */
.btn {
  width: 100%;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: .75rem;
  font-weight: 700;
  letter-spacing: .3px;
  font-size: 1rem;
  cursor: pointer;
  transition: transform .05s ease, filter .2s ease, box-shadow .2s ease;
  margin-top: 1rem;
}

.btn-primary {
  color: #0b1220;
  background: linear-gradient(180deg, rgba(255,255,255,.25), rgba(255,255,255,0)) padding-box,
              linear-gradient(135deg, var(--primary), #3b82f6) border-box;
  border: 1px solid transparent;
}

.btn:hover { 
  filter: brightness(1.05);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(34, 211, 238, 0.3);
}

.btn:active { 
  transform: translateY(1px); 
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  filter: none;
  box-shadow: none;
}

/* Links Section */
.links {
  margin-top: 1.5rem;
  text-align: center;
  font-size: .9rem;
}

/* Footer */
footer { 
  margin-top: 2rem; 
  text-align: center; 
  color: var(--muted); 
  font-size: .85rem; 
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .card {
    max-width: 700px;
  }
}

@media (max-width: 768px) {
  .nav-content {
    padding: 0 0.5rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
  }

  body {
    padding: 0.5rem;
    padding-top: 4rem;
  }

  .card {
    padding: 1.5rem;
    margin: 0.5rem 0;
    max-width: 100%;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .form-section {
    padding: 1rem;
  }

  h1 {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem 0.875rem;
  }

  .btn {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }

  .checkbox-label {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  body {
    padding: 0.25rem;
  }

  .card {
    padding: 1rem;
    border-radius: 1rem;
  }

  .form-section {
    padding: 0.75rem;
    border-radius: 0.75rem;
  }

  .input {
    padding: 0.625rem 0.75rem;
    border-radius: 0.5rem;
  }

  .btn {
    padding: 0.75rem 0.875rem;
    border-radius: 0.5rem;
  }

  h1 {
    font-size: 1.25rem;
  }

  p {
    font-size: 0.95rem;
  }

  .section-title {
    font-size: 1rem;
  }

  .logo {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .checkbox-label {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .input-hint {
    font-size: 0.75rem;
  }
}

@media (max-width: 320px) {
  .card {
    padding: 0.75rem;
  }

  .form-section {
    padding: 0.5rem;
  }

  .input {
    font-size: 14px;
    padding: 0.5rem 0.625rem;
  }

  .btn {
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
  }

  h1 {
    font-size: 1.125rem;
  }

  .section-title {
    font-size: 0.95rem;
  }

  .logo {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
}

/* Improved touch targets for mobile */
@media (max-width: 768px) {
  .checkbox {
    width: 20px;
    height: 20px;
    margin-top: 1px;
  }

  .checkbox-label {
    gap: 0.875rem;
  }

  .link {
    padding: 0.25rem;
    margin: -0.25rem;
  }
}

/* Better focus states for accessibility */
.input:focus,
.btn:focus,
.checkbox:focus,
.link:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Improved form validation states */
.input:invalid:not(:placeholder-shown):not(:focus) {
  border-color: var(--error);
  background: rgba(239, 68, 68, 0.05);
}

.input:valid:not(:placeholder-shown):not(:focus) {
  border-color: var(--success);
  background: rgba(16, 185, 129, 0.05);
}

/* Better error message styling */
.error-messages {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

/* Smooth transitions for form sections */
.form-section {
  transition: all 0.3s ease;
}

.form-section:hover {
  border-color: rgba(148, 163, 184, 0.3);
}

/* Loading state improvements */
.btn.loading {
  pointer-events: none;
  opacity: 0.8;
}

/* Print styles */
@media print {
  body {
    background: white;
    color: black;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
    background: white;
  }

  .btn,
  .error-messages {
    display: none;
  }
}

/* Animation for form sections */
.form-section {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state for button */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #0b1220;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
