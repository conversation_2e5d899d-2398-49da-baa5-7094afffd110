:root {
  --bg: #0f172a;        /* slate-900 */
  --card: #111827;      /* gray-900 */
  --muted: #94a3b8;     /* slate-400 */
  --text: #e5e7eb;      /* gray-200 */
  --primary: #22d3ee;   /* cyan-400 */
  --primary-700: #0e7490;/* cyan-700 */
  --ring: rgba(34, 211, 238, 0.45);
  --error: #ef4444;     /* red-500 */
}

* { box-sizing: border-box; }
html, body { height: 100%; }

/* Navigation */
.nav-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.15);
  z-index: 1000;
  padding: 0.75rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  color: var(--text);
  text-decoration: none;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary), #3b82f6);
  border-radius: 8px;
  display: grid;
  place-items: center;
  font-size: 1rem;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.nav-link {
  color: var(--text);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: var(--primary);
  background: rgba(34, 211, 238, 0.1);
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  background: radial-gradient(1200px 800px at 80% -10%, rgba(34,211,238,.12), transparent 60%),
              radial-gradient(900px 700px at -10% 110%, rgba(59,130,246,.10), transparent 60%),
              var(--bg);
  color: var(--text);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  padding-top: 5rem; /* Account for fixed navigation */
  min-height: 100vh;
}

.card {
  width: 100%;
  max-width: 420px;
  background: linear-gradient(180deg, rgba(255,255,255,0.02), transparent 40%), var(--card);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 1.25rem;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35), inset 0 1px 0 rgba(255,255,255,0.02);
  padding: 2rem;
}

.logo {
  width: 48px; height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary), #3b82f6);
  display: grid; place-items: center;
  color: #0b1220;
  font-weight: 800;
  margin-bottom: 1rem;
}

h1 { font-size: 1.5rem; margin: 0 0 .25rem; }
p { margin: 0 0 1.25rem; color: var(--muted); }

form { display: grid; gap: 1rem; }

label { font-size: .9rem; color: #cbd5e1; display: block; margin-bottom: .4rem; }

.input {
  width: 100%;
  border: 1px solid rgba(148,163,184,.2);
  background: rgba(2,6,23,.6);
  color: var(--text);
  padding: .85rem 1rem;
  border-radius: .9rem;
  outline: none;
  transition: border-color .2s, box-shadow .2s, background .2s;
}
.input::placeholder { color: #94a3b8; }
.input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px var(--ring);
  background: rgba(2,6,23,.9);
}

.row { display: flex; align-items: center; justify-content: space-between; gap: .75rem; }
.row .left { display: flex; align-items: center; gap: .5rem; }
.checkbox { width: 18px; height: 18px; }

.btn {
  width: 100%;
  border: none;
  padding: .95rem 1rem;
  border-radius: .9rem;
  font-weight: 700;
  letter-spacing: .2px;
  color: #0b1220;
  background: linear-gradient(180deg, rgba(255,255,255,.25), rgba(255,255,255,0)) padding-box,
              linear-gradient(135deg, var(--primary), #3b82f6) border-box;
  border: 1px solid transparent;
  cursor: pointer;
  transition: transform .05s ease, filter .2s ease;
}
.btn:hover { filter: brightness(1.05); }
.btn:active { transform: translateY(1px); }

.links {
  margin-top: .75rem;
  display: flex; justify-content: space-between; gap: .75rem; flex-wrap: wrap;
  font-size: .9rem;
}
a { color: var(--primary); text-decoration: none; }
a:hover { text-decoration: underline; }

.sr-only {
  position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); border: 0;
}

.error { color: var(--error); font-size: .9rem; display: none; }

footer { margin-top: 1.25rem; text-align: center; color: var(--muted); font-size: .85rem; }

/* Responsive Design */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 0.5rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
  }

  body {
    padding: 0.5rem;
    padding-top: 4rem;
  }

  .card {
    padding: 1.5rem;
    max-width: 100%;
    margin: 0;
  }

  .logo {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  h1 {
    font-size: 1.25rem;
  }

  p {
    font-size: 0.95rem;
  }

  .input {
    padding: 0.75rem 0.875rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .btn {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }

  .links {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .row .left {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  body {
    padding: 0.25rem;
  }

  .card {
    padding: 1rem;
    border-radius: 1rem;
  }

  .logo {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  h1 {
    font-size: 1.125rem;
  }

  .input {
    padding: 0.625rem 0.75rem;
  }

  .btn {
    padding: 0.75rem 0.875rem;
  }

  footer {
    font-size: 0.75rem;
  }
}

@media (max-width: 320px) {
  .card {
    padding: 0.75rem;
  }

  .input {
    font-size: 14px;
  }
}

/* Improved focus states for accessibility */
.input:focus,
.btn:focus,
.checkbox:focus,
a:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Better checkbox styling */
.checkbox {
  appearance: none;
  border: 2px solid rgba(148,163,184,.4);
  border-radius: 4px;
  background: rgba(2,6,23,.6);
  cursor: pointer;
  position: relative;
}

.checkbox:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #0b1220;
  font-weight: bold;
  font-size: 12px;
}

/* Loading state for button */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #0b1220;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}