<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TechCorp - Innovative Solutions for Tomorrow</title>
  <link rel="stylesheet" href="../CSS/homepage.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-logo">
        <div class="logo-icon">🚀</div>
        <span class="logo-text">TechCorp</span>
      </div>
      <ul class="nav-menu">
        <li><a href="#home" class="nav-link">Home</a></li>
        <li><a href="#about" class="nav-link">About</a></li>
        <li><a href="#services" class="nav-link">Services</a></li>
        <li><a href="#contact" class="nav-link">Contact</a></li>
        <li><a href="#" class="nav-link login-btn" id="loginBtn">Login</a></li>
      </ul>
      <div class="hamburger">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Login Modal -->
  <div id="loginModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h2>Sign In</h2>
        <button class="modal-close" id="closeModal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="login-logo">🔒</div>
        <p class="login-subtitle">Welcome back. Please enter your details.</p>

        <form id="loginForm" class="login-form" novalidate>
          <div class="form-group">
            <label for="modalEmail">Email</label>
            <input class="form-input" type="email" id="modalEmail" name="email" placeholder="<EMAIL>" required />
          </div>

          <div class="form-group">
            <div class="password-header">
              <label for="modalPassword">Password</label>
              <a href="#" class="forgot-link">Forgot?</a>
            </div>
            <input class="form-input" type="password" id="modalPassword" name="password" placeholder="••••••••" minlength="6" required />
          </div>

          <div class="form-options">
            <label class="checkbox-label">
              <input type="checkbox" id="modalRemember" name="remember" class="checkbox" />
              <span class="checkmark"></span>
              <span>Remember me</span>
            </label>
          </div>

          <div class="error-message" id="loginError" style="display: none;"></div>

          <button class="btn btn-primary" type="submit">Sign In</button>

          <div class="modal-links">
            <span>New here? <a href="#" class="register-link" id="showRegister">Create account</a></span>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Register Modal -->
  <div id="registerModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content register-modal">
      <div class="modal-header">
        <h2>Create Account</h2>
        <button class="modal-close" id="closeRegisterModal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="login-logo">📝</div>
        <p class="login-subtitle">Join us today. Please fill in your details below.</p>

        <form id="registerForm" class="register-form" novalidate>
          <!-- Personal Information -->
          <div class="form-section">
            <h4 class="section-title">Personal Information</h4>

            <div class="form-row">
              <div class="form-group">
                <label for="regIdNumber">ID Number *</label>
                <input class="form-input" type="text" id="regIdNumber" name="idNumber" placeholder="Enter your ID number" required />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="regFirstName">First Name *</label>
                <input class="form-input" type="text" id="regFirstName" name="firstName" placeholder="Enter your first name" required />
              </div>
              <div class="form-group">
                <label for="regMiddleName">Middle Name</label>
                <input class="form-input" type="text" id="regMiddleName" name="middleName" placeholder="Enter your middle name" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="regLastName">Last Name *</label>
                <input class="form-input" type="text" id="regLastName" name="lastName" placeholder="Enter your last name" required />
              </div>
              <div class="form-group">
                <label for="regExtension">Extension Name</label>
                <input class="form-input" type="text" id="regExtension" name="extensionName" placeholder="Jr., Sr., III, etc." />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="regAge">Age *</label>
                <input class="form-input" type="number" id="regAge" name="age" placeholder="Enter your age" min="13" max="120" required />
              </div>
              <div class="form-group">
                <label for="regSex">Sex *</label>
                <select class="form-input" id="regSex" name="sex" required>
                  <option value="">Select your sex</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer-not-to-say">Prefer not to say</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="form-section">
            <h4 class="section-title">Contact Information</h4>

            <div class="form-row">
              <div class="form-group full-width">
                <label for="regAddress">Address *</label>
                <textarea class="form-input" id="regAddress" name="address" placeholder="Enter your complete address" rows="2" required></textarea>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="regZipcode">Zipcode *</label>
                <input class="form-input" type="text" id="regZipcode" name="zipcode" placeholder="Enter your zipcode" required />
              </div>
              <div class="form-group">
                <label for="regEmail">Email Address *</label>
                <input class="form-input" type="email" id="regEmail" name="email" placeholder="<EMAIL>" required />
              </div>
            </div>
          </div>

          <!-- Account Information -->
          <div class="form-section">
            <h4 class="section-title">Account Information</h4>

            <div class="form-row">
              <div class="form-group">
                <label for="regUsername">Username *</label>
                <input class="form-input" type="text" id="regUsername" name="username" placeholder="Choose a username" required />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="regPassword">Password *</label>
                <input class="form-input" type="password" id="regPassword" name="password" placeholder="Create a strong password" minlength="8" required />
              </div>
              <div class="form-group">
                <label for="regConfirmPassword">Re-enter Password *</label>
                <input class="form-input" type="password" id="regConfirmPassword" name="confirmPassword" placeholder="Confirm your password" minlength="8" required />
              </div>
            </div>
          </div>

          <!-- Terms -->
          <div class="form-section">
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" id="regTerms" name="terms" class="checkbox" required />
                <span class="checkmark"></span>
                <span>I agree to the <a href="#" class="register-link">Terms and Conditions</a> and <a href="#" class="register-link">Privacy Policy</a> *</span>
              </label>
            </div>
          </div>

          <div class="error-message" id="registerError" style="display: none;"></div>

          <button class="btn btn-primary" type="submit">Create Account</button>

          <div class="modal-links">
            <span>Already have an account? <a href="#" class="register-link" id="showLogin">Sign in here</a></span>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Hero Section -->
  <section id="home" class="hero">
    <div class="hero-container">
      <div class="hero-content">
        <h1 class="hero-title">
          Building the Future with
          <span class="gradient-text">Innovation</span>
        </h1>
        <p class="hero-description">
          We create cutting-edge solutions that transform businesses and empower teams to achieve extraordinary results in the digital age.
        </p>
        <div class="hero-buttons">
          <button class="btn btn-primary">Get Started</button>
          <button class="btn btn-secondary">Learn More</button>
        </div>
      </div>
      <div class="hero-image">
        <div class="floating-card card-1">
          <div class="card-icon">📊</div>
          <div class="card-text">Analytics</div>
        </div>
        <div class="floating-card card-2">
          <div class="card-icon">🔒</div>
          <div class="card-text">Security</div>
        </div>
        <div class="floating-card card-3">
          <div class="card-icon">⚡</div>
          <div class="card-text">Performance</div>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">About TechCorp</h2>
        <p class="section-subtitle">Leading innovation in technology solutions</p>
      </div>
      <div class="about-grid">
        <div class="about-content">
          <h3>Our Mission</h3>
          <p>To empower businesses with innovative technology solutions that drive growth, efficiency, and success in an ever-evolving digital landscape.</p>
          <div class="stats">
            <div class="stat">
              <div class="stat-number">500+</div>
              <div class="stat-label">Projects Completed</div>
            </div>
            <div class="stat">
              <div class="stat-number">50+</div>
              <div class="stat-label">Team Members</div>
            </div>
            <div class="stat">
              <div class="stat-number">99%</div>
              <div class="stat-label">Client Satisfaction</div>
            </div>
          </div>
        </div>
        <div class="about-image">
          <div class="image-placeholder">
            <div class="placeholder-icon">🏢</div>
            <p>Company Image</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="services">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Our Services</h2>
        <p class="section-subtitle">Comprehensive solutions for your business needs</p>
      </div>
      <div class="services-grid">
        <div class="service-card">
          <div class="service-icon">💻</div>
          <h3>Web Development</h3>
          <p>Custom web applications built with modern technologies and best practices.</p>
          <ul>
            <li>Responsive Design</li>
            <li>Performance Optimization</li>
            <li>SEO Friendly</li>
          </ul>
        </div>
        <div class="service-card">
          <div class="service-icon">📱</div>
          <h3>Mobile Apps</h3>
          <p>Native and cross-platform mobile applications for iOS and Android.</p>
          <ul>
            <li>iOS Development</li>
            <li>Android Development</li>
            <li>Cross-platform Solutions</li>
          </ul>
        </div>
        <div class="service-card">
          <div class="service-icon">☁️</div>
          <h3>Cloud Solutions</h3>
          <p>Scalable cloud infrastructure and deployment solutions.</p>
          <ul>
            <li>AWS & Azure</li>
            <li>DevOps & CI/CD</li>
            <li>Microservices</li>
          </ul>
        </div>
        <div class="service-card">
          <div class="service-icon">🔐</div>
          <h3>Cybersecurity</h3>
          <p>Comprehensive security solutions to protect your digital assets.</p>
          <ul>
            <li>Security Audits</li>
            <li>Penetration Testing</li>
            <li>Compliance</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Get In Touch</h2>
        <p class="section-subtitle">Ready to start your next project? Let's talk!</p>
      </div>
      <div class="contact-grid">
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">📧</div>
            <div>
              <h4>Email</h4>
              <p><EMAIL></p>
              <p><EMAIL></p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📞</div>
            <div>
              <h4>Phone</h4>
              <p>+****************</p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📍</div>
            <div>
              <h4>Address</h4>
              <p>T. Curato Street<br>Cabadbaran City, 8605</p>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-logo">
            <div class="logo-icon">🚀</div>
            <span class="logo-text">TechCorp</span>
          </div>
          <p>Innovative solutions for tomorrow's challenges.</p>
        </div>
        <div class="footer-section">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#about">About</a></li>
            <li><a href="#services">Services</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>Services</h4>
          <ul>
            <li><a href="#">Web Development</a></li>
            <li><a href="#">Mobile Apps</a></li>
            <li><a href="#">Cloud Solutions</a></li>
            <li><a href="#">Cybersecurity</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>Connect</h4>
          <div class="social-links">
            <a href="#" aria-label="Twitter">🐦</a>
            <a href="#" aria-label="LinkedIn">💼</a>
            <a href="#" aria-label="GitHub">🐙</a>
            <a href="#" aria-label="Email">📧</a>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 TechCorp. All rights reserved.</p>
        <p>Maxcel & Louie</p>
      
      </div>
    </div>
  </footer>

  <script>
    // Modal functionality
    const loginModal = document.getElementById('loginModal');
    const registerModal = document.getElementById('registerModal');
    const loginBtn = document.getElementById('loginBtn');
    const closeModal = document.getElementById('closeModal');
    const closeRegisterModal = document.getElementById('closeRegisterModal');
    const showRegister = document.getElementById('showRegister');
    const showLogin = document.getElementById('showLogin');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');

    // Open login modal
    loginBtn.addEventListener('click', (e) => {
      e.preventDefault();
      loginModal.classList.add('active');
      document.body.style.overflow = 'hidden';
    });

    // Close modals
    closeModal.addEventListener('click', () => {
      loginModal.classList.remove('active');
      document.body.style.overflow = 'auto';
    });

    closeRegisterModal.addEventListener('click', () => {
      registerModal.classList.remove('active');
      document.body.style.overflow = 'auto';
    });

    // Switch between login and register
    showRegister.addEventListener('click', (e) => {
      e.preventDefault();
      loginModal.classList.remove('active');
      registerModal.classList.add('active');
    });

    showLogin.addEventListener('click', (e) => {
      e.preventDefault();
      registerModal.classList.remove('active');
      loginModal.classList.add('active');
    });

    // Close modal when clicking overlay
    document.querySelectorAll('.modal-overlay').forEach(overlay => {
      overlay.addEventListener('click', () => {
        loginModal.classList.remove('active');
        registerModal.classList.remove('active');
        document.body.style.overflow = 'auto';
      });
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        loginModal.classList.remove('active');
        registerModal.classList.remove('active');
        document.body.style.overflow = 'auto';
      }
    });

    
  </script>
