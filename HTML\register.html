<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Register - Create Your Account</title>
  <link rel="stylesheet" href="../CSS/register.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="nav-header">
    <div class="nav-content">
      <div class="nav-logo">
        <div class="logo-icon">🚀</div>
        <span>TechCorp</span>
      </div>
      <div class="nav-links">
        <a href="homepage.html" class="nav-link">Home</a>
        <a href="index.html" class="nav-link">Login</a>
      </div>
    </div>
  </nav>

  <main class="card" aria-labelledby="title">
    <div class="logo" aria-hidden="true">📝</div>
    <h1 id="title">Create Account</h1>
    <p>Join us today. Please fill in your details below.</p>

    <form action="/register" method="post" novalidate class="registration-form">
      <!-- Personal Information Section -->
      <div class="form-section">
        <h3 class="section-title">Personal Information</h3>
        
        <div class="form-row">
          <div class="form-group full-width">
            <label for="idNumber">ID Number *</label>
            <input class="input" type="text" id="idNumber" name="idNumber" placeholder="Enter your ID number" required />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="firstName">First Name *</label>
            <input class="input" type="text" id="firstName" name="firstName" placeholder="Enter your first name" required />
          </div>
          <div class="form-group">
            <label for="middleName">Middle Name</label>
            <input class="input" type="text" id="middleName" name="middleName" placeholder="Enter your middle name" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="lastName">Last Name *</label>
            <input class="input" type="text" id="lastName" name="lastName" placeholder="Enter your last name" required />
          </div>
          <div class="form-group">
            <label for="extensionName">Extension Name</label>
            <input class="input" type="text" id="extensionName" name="extensionName" placeholder="Jr., Sr., III, etc." />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="age">Age *</label>
            <input class="input" type="number" id="age" name="age" placeholder="Enter your age" min="13" max="120" required />
          </div>
          <div class="form-group">
            <label for="sex">Sex *</label>
            <select class="input" id="sex" name="sex" required>
              <option value="">Select your sex</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
              <option value="prefer-not-to-say">Prefer not to say</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Contact Information Section -->
      <div class="form-section">
        <h3 class="section-title">Contact Information</h3>
        
        <div class="form-row">
          <div class="form-group full-width">
            <label for="address">Address *</label>
            <textarea class="input" id="address" name="address" placeholder="Enter your complete address" rows="3" required></textarea>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="zipcode">Zipcode *</label>
            <input class="input" type="text" id="zipcode" name="zipcode" placeholder="Enter your zipcode" required />
          </div>
          <div class="form-group">
            <label for="email">Email Address *</label>
            <input class="input" type="email" id="email" name="email" placeholder="<EMAIL>" required />
          </div>
        </div>
      </div>

      <!-- Account Information Section -->
      <div class="form-section">
        <h3 class="section-title">Account Information</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="username">Username *</label>
            <input class="input" type="text" id="username" name="username" placeholder="Choose a username" required />
            <small class="input-hint">Username must be 3-20 characters long</small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="password">Password *</label>
            <input class="input" type="password" id="password" name="password" placeholder="Create a strong password" minlength="8" required />
            <small class="input-hint">Password must be at least 8 characters</small>
          </div>
          <div class="form-group">
            <label for="confirmPassword">Re-enter Password *</label>
            <input class="input" type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" minlength="8" required />
          </div>
        </div>
      </div>

      <!-- Terms and Conditions -->
      <div class="form-section">
        <div class="checkbox-group">
          <label class="checkbox-label" for="terms">
            <input class="checkbox" type="checkbox" id="terms" name="terms" required />
            <span class="checkmark"></span>
            <span>I agree to the <a href="#" class="link">Terms and Conditions</a> and <a href="#" class="link">Privacy Policy</a> *</span>
          </label>
        </div>

        <div class="checkbox-group">
          <label class="checkbox-label" for="newsletter">
            <input class="checkbox" type="checkbox" id="newsletter" name="newsletter" />
            <span class="checkmark"></span>
            <span>I would like to receive newsletters and updates</span>
          </label>
        </div>
      </div>

      <!-- Error Messages -->
      <div class="error-messages" id="errorMessages" style="display: none;">
        <ul id="errorList"></ul>
      </div>

      <!-- Submit Button -->
      <button class="btn btn-primary" type="submit">Create Account</button>

      <!-- Login Link -->
      <div class="links">
        <span>Already have an account? <a href="index.html" class="link">Sign in here</a></span>
      </div>
    </form>

    <footer>By creating an account, you agree to our <a href="#" class="link">Terms</a> &amp; <a href="#" class="link">Privacy Policy</a>.</footer>
  </main>

