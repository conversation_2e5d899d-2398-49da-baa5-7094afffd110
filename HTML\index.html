<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Login</title>
  <link rel="stylesheet" href="../CSS/style.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="nav-header">
    <div class="nav-content">
      <div class="nav-logo">
        <div class="logo-icon">🚀</div>
        <span>TechCorp</span>
      </div>
      <div class="nav-links">
        <a href="homepage.html" class="nav-link">Home</a>
        <a href="register.html" class="nav-link">Register</a>
      </div>
    </div>
  </nav>

  <main class="card" aria-labelledby="title">
    <div class="logo" aria-hidden="true">🔒</div>
    <h1 id="title">Sign in</h1>
    <p>Welcome back. Please enter your details.</p>

    <!-- Note: This is HTML-only. The form posts to /login for your backend to process. -->
    <form action="/login" method="post" novalidate>
      <div>
        <label for="email">Email</label>
        <input class="input" type="email" id="email" name="email" placeholder="<EMAIL>" required />
      </div>

      <div>
        <div class="row" style="justify-content: space-between;">
          <label for="password">Password</label>
          <a href="#" aria-label="Forgot password?">Forgot?</a>
        </div>
        <input class="input" type="password" id="password" name="password" placeholder="••••••••" minlength="6" required />
        <p class="error" id="pwError" aria-live="polite">Password must be at least 6 characters.</p>
      </div>

      <div class="row">
        <label class="left" for="remember">
          <input class="checkbox" type="checkbox" id="remember" name="remember" />
          <span>Remember me</span>
        </label>
        <span class="sr-only" aria-live="polite">&nbsp;</span>
      </div>

      <button class="btn" type="submit">Sign in</button>

      <div class="links">
        <span>New here? <a href="register.html">Create account</a></span>
        <a href="#" aria-label="Need help? Read the sign in help">Need help?</a>
      </div>
    </form>

    <footer>By continuing, you agree to our <a href="#">Terms</a> &amp; <a href="#">Privacy</a>.</footer>
  </main>

  