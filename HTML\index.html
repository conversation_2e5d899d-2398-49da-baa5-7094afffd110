<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Login</title>
  <style>
    :root {
      --bg: #0f172a;        /* slate-900 */
      --card: #111827;      /* gray-900 */
      --muted: #94a3b8;     /* slate-400 */
      --text: #e5e7eb;      /* gray-200 */
      --primary: #22d3ee;   /* cyan-400 */
      --primary-700: #0e7490;/* cyan-700 */
      --ring: rgba(34, 211, 238, 0.45);
      --error: #ef4444;     /* red-500 */
    }

    * { box-sizing: border-box; }
    html, body { height: 100%; }

    body {
      margin: 0;
      font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
      background: radial-gradient(1200px 800px at 80% -10%, rgba(34,211,238,.12), transparent 60%),
                  radial-gradient(900px 700px at -10% 110%, rgba(59,130,246,.10), transparent 60%),
                  var(--bg);
      color: var(--text);
      display: grid;
      place-items: center;
      padding: 2rem;
    }

    .card {
      width: 100%;
      max-width: 420px;
      background: linear-gradient(180deg, rgba(255,255,255,0.02), transparent 40%), var(--card);
      border: 1px solid rgba(148, 163, 184, 0.15);
      border-radius: 1.25rem;
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35), inset 0 1px 0 rgba(255,255,255,0.02);
      padding: 2rem;
    }

    .logo {
      width: 48px; height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, var(--primary), #3b82f6);
      display: grid; place-items: center;
      color: #0b1220;
      font-weight: 800;
      margin-bottom: 1rem;
    }

    h1 { font-size: 1.5rem; margin: 0 0 .25rem; }
    p { margin: 0 0 1.25rem; color: var(--muted); }

    form { display: grid; gap: 1rem; }

    label { font-size: .9rem; color: #cbd5e1; display: block; margin-bottom: .4rem; }

    .input {
      width: 100%;
      border: 1px solid rgba(148,163,184,.2);
      background: rgba(2,6,23,.6);
      color: var(--text);
      padding: .85rem 1rem;
      border-radius: .9rem;
      outline: none;
      transition: border-color .2s, box-shadow .2s, background .2s;
    }
    .input::placeholder { color: #94a3b8; }
    .input:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 4px var(--ring);
      background: rgba(2,6,23,.9);
    }

    .row { display: flex; align-items: center; justify-content: space-between; gap: .75rem; }
    .row .left { display: flex; align-items: center; gap: .5rem; }
    .checkbox { width: 18px; height: 18px; }

    .btn {
      width: 100%;
      border: none;
      padding: .95rem 1rem;
      border-radius: .9rem;
      font-weight: 700;
      letter-spacing: .2px;
      color: #0b1220;
      background: linear-gradient(180deg, rgba(255,255,255,.25), rgba(255,255,255,0)) padding-box,
                  linear-gradient(135deg, var(--primary), #3b82f6) border-box;
      border: 1px solid transparent;
      cursor: pointer;
      transition: transform .05s ease, filter .2s ease;
    }
    .btn:hover { filter: brightness(1.05); }
    .btn:active { transform: translateY(1px); }

    .links {
      margin-top: .75rem;
      display: flex; justify-content: space-between; gap: .75rem; flex-wrap: wrap;
      font-size: .9rem;
    }
    a { color: var(--primary); text-decoration: none; }
    a:hover { text-decoration: underline; }

    .sr-only {
      position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); border: 0;
    }

    .error { color: var(--error); font-size: .9rem; display: none; }

    footer { margin-top: 1.25rem; text-align: center; color: var(--muted); font-size: .85rem; }
  </style>
</head>
<body>
  <main class="card" aria-labelledby="title">
    <div class="logo" aria-hidden="true">🔒</div>
    <h1 id="title">Sign in</h1>
    <p>Welcome back. Please enter your details.</p>

    <!-- Note: This is HTML-only. The form posts to /login for your backend to process. -->
    <form action="/login" method="post" novalidate>
      <div>
        <label for="email">Email</label>
        <input class="input" type="email" id="email" name="email" placeholder="<EMAIL>" required />
      </div>

      <div>
        <div class="row" style="justify-content: space-between;">
          <label for="password">Password</label>
          <a href="#" aria-label="Forgot password?">Forgot?</a>
        </div>
        <input class="input" type="password" id="password" name="password" placeholder="••••••••" minlength="6" required />
        <p class="error" id="pwError" aria-live="polite">Password must be at least 6 characters.</p>
      </div>

      <div class="row">
        <label class="left" for="remember">
          <input class="checkbox" type="checkbox" id="remember" name="remember" />
          <span>Remember me</span>
        </label>
        <span class="sr-only" aria-live="polite">&nbsp;</span>
      </div>

      <button class="btn" type="submit">Sign in</button>

      <div class="links">
        <span>New here? <a href="#">Create account</a></span>
        <a href="#" aria-label="Need help? Read the sign in help">Need help?</a>
      </div>
    </form>

    <footer>By continuing, you agree to our <a href="#">Terms</a> &amp; <a href="#">Privacy</a>.</footer>
  </main>
</body>
</html>
