/* CSS Variables */
:root {
  --primary: #22d3ee;
  --primary-dark: #0e7490;
  --secondary: #3b82f6;
  --bg-dark: #0f172a;
  --bg-card: #111827;
  --text-light: #e5e7eb;
  --text-muted: #94a3b8;
  --text-dark: #1f2937;
  --border: rgba(148, 163, 184, 0.15);
  --shadow: rgba(0, 0, 0, 0.35);
  --gradient-1: linear-gradient(135deg, var(--primary), var(--secondary));
  --gradient-2: linear-gradient(180deg, rgba(255,255,255,0.02), transparent 40%);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
  background: var(--bg-dark);
  color: var(--text-light);
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border);
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 800;
  font-size: 1.25rem;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient-1);
  border-radius: 10px;
  display: grid;
  place-items: center;
  font-size: 1.2rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--primary);
}

.login-btn {
  background: var(--gradient-1);
  color: var(--text-dark) !important;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-light);
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: radial-gradient(1200px 800px at 80% -10%, rgba(34,211,238,.12), transparent 60%),
              radial-gradient(900px 700px at -10% 110%, rgba(59,130,246,.10), transparent 60%),
              var(--bg-dark);
  padding-top: 80px;
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-left: 200px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.gradient-text {
  background: var(--gradient-1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: var(--text-muted);
  margin-bottom: 2rem;
  line-height: 1.7;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: var(--gradient-1);
  color: var(--text-dark);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(34, 211, 238, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--text-light);
  border: 2px solid var(--primary);
}

.btn-secondary:hover {
  background: var(--primary);
  color: var(--text-dark);
}

/* Hero Image */
.hero-image {
  position: relative;
  height: 400px;
}

.floating-card {
  position: absolute;
  background: var(--gradient-2), var(--bg-card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 10px 30px var(--shadow);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  animation: float 6s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  right: 20%;
  animation-delay: 2s;
}

.card-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

.card-icon {
  font-size: 2rem;
}

.card-text {
  font-weight: 600;
  color: var(--primary);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.25rem;
  color: var(--text-muted);
}

/* About Section */
.about {
  padding: 6rem 0;
  background: var(--bg-card);
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-content h3 {
  font-size: 1.75rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.about-content p {
  font-size: 1.1rem;
  color: var(--text-muted);
  margin-bottom: 2rem;
}

.stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary);
}

.stat-label {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.about-image {
  display: flex;
  justify-content: center;
}

.image-placeholder {
  width: 300px;
  height: 300px;
  background: var(--gradient-2), var(--bg-dark);
  border: 2px dashed var(--border);
  border-radius: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Services Section */
.services {
  padding: 6rem 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.service-card {
  background: var(--gradient-2), var(--bg-card);
  border: 1px solid var(--border);
  border-radius: 1.25rem;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px var(--shadow);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.service-card p {
  color: var(--text-muted);
  margin-bottom: 1.5rem;
}

.service-card ul {
  list-style: none;
  text-align: left;
}

.service-card li {
  padding: 0.25rem 0;
  color: var(--text-muted);
  position: relative;
  padding-left: 1.5rem;
}

.service-card li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary);
  font-weight: bold;
}

/* Contact Section */
.contact {
  padding: 6rem 0;
  background: var(--bg-card);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: var(--gradient-1);
  border-radius: 50%;
  display: grid;
  place-items: center;
  font-size: 1.25rem;
}

.contact-item h4 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  color: var(--primary);
}

.contact-item p {
  color: var(--text-muted);
}

.contact-form {
  background: var(--gradient-2), var(--bg-dark);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-light);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  background: rgba(2,6,23,.6);
  color: var(--text-light);
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(34, 211, 238, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* Footer */
.footer {
  background: var(--bg-dark);
  border-top: 1px solid var(--border);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  color: var(--primary);
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: var(--text-muted);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--primary);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 800;
  font-size: 1.25rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: 50%;
  display: grid;
  place-items: center;
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--primary);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border);
  color: var(--text-muted);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background: linear-gradient(180deg, rgba(255,255,255,0.02), transparent 40%), var(--bg-card);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 1.25rem;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255,255,255,0.02);
  width: 90%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
}

.modal.active .modal-content {
  transform: scale(1) translateY(0);
}

.register-modal {
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem 1rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.15);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-light);
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-light);
}

.modal-body {
  padding: 1.5rem 2rem 2rem;
}

.login-logo {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: grid;
  place-items: center;
  color: #0b1220;
  font-weight: 800;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
}

.login-subtitle {
  text-align: center;
  color: var(--text-muted);
  margin-bottom: 2rem;
  font-size: 1rem;
}

.login-form,
.register-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 0.9rem;
  color: #cbd5e1;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-input {
  width: 100%;
  border: 1px solid rgba(148,163,184,.2);
  background: rgba(2,6,23,.6);
  color: var(--text-light);
  padding: 0.85rem 1rem;
  border-radius: 0.75rem;
  outline: none;
  transition: border-color .2s, box-shadow .2s, background .2s;
  font-size: 1rem;
  min-height: 44px;
}

.form-input::placeholder {
  color: var(--text-muted);
}

.form-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(34, 211, 238, 0.1);
  background: rgba(2,6,23,.9);
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.forgot-link {
  color: var(--primary);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.forgot-link:hover {
  text-decoration: underline;
}

.form-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.5;
}

.checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(148,163,184,.4);
  border-radius: 4px;
  background: rgba(2,6,23,.6);
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
}

.checkbox:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #0b1220;
  font-weight: bold;
  font-size: 12px;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: #ef4444;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}

.modal-links {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.9rem;
}

.register-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.register-link:hover {
  text-decoration: underline;
}

/* Register Modal Specific Styles */
.form-section {
  background: rgba(2,6,23,.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.checkbox-group {
  margin-bottom: 1rem;
}

/* Loading state for buttons */
.btn.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #0b1220;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background: var(--bg-dark);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
    

  }

  .hero-title {
    font-size: 2.5rem;
    
  }

  .about-grid,
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .container {
    padding: 0 1rem;
  }

  .nav-container {
    padding: 1rem;
  }

  /* Modal responsive styles */
  .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body {
    padding: 1rem 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .register-modal {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    border-radius: 1rem;
  }

  .modal-header,
  .modal-body {
    padding: 1rem;
  }

  .modal-header h2 {
    font-size: 1.25rem;
  }

  .form-section {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .login-logo {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .form-input {
    padding: 0.75rem;
    font-size: 16px;
  }

  .btn {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }
}
